@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Form Submissions') }}: {{ $form->form_name }}</h5>
                    <div>
                        <a href="{{ route('super-admin.form-builder.index') }}" class="btn btn-sm btn-secondary">Back to Forms</a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Form Details:</strong></p>
                        <ul>
                            <li><strong>Form Name:</strong> {{ $form->form_name }}</li>
                            <li><strong>Target Users:</strong>
                                @if($form->target_user == 'all_scientists')
                                    All Scientists
                                @elseif($form->target_user == 'pre_cocoon')
                                    Pre-Cocoon Scientists
                                @elseif($form->target_user == 'post_cocoon')
                                    Post-Cocoon Scientists
                                @endif
                            </li>
                            <li><strong>Last Date:</strong>
                                @if($form->last_date)
                                    {{ $form->last_date->format('d M Y, h:i A') }}
                                @else
                                    No deadline
                                @endif
                            </li>
                            <li><strong>Total Submissions:</strong> {{ $submissions->count() }}</li>
                        </ul>
                    </div>

                    @if($submissions->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Submitted By</th>
                                        <th>Submission Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($submissions as $index => $submission)
                                        <tr>
                                            <td>{{ $index + 1 }}</td>
                                            <td>
                                                @if(isset($submission->name) && isset($submission->email))
                                                    {{ $submission->name }} <br>
                                                    <small class="text-muted">{{ $submission->email }}</small>
                                                @else
                                                    Unknown User
                                                @endif
                                            </td>
                                            <td>{{ isset($submission->created_at) ? \Carbon\Carbon::parse($submission->created_at)->format('d M Y, h:i A') : 'N/A' }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-primary view-submission-btn"
                                                        data-id="{{ $submission->id }}"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#viewSubmissionModal">
                                                    <i class="bi bi-eye"></i> View
                                                </button>
                                                <button class="btn btn-sm btn-danger delete-submission-btn"
                                                        data-id="{{ $submission->id }}">
                                                    <i class="bi bi-trash"></i> Delete
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-warning">
                            <p>No submissions found for this form.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- View Submission Modal -->
<div class="modal fade" id="viewSubmissionModal" tabindex="-1" aria-labelledby="viewSubmissionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewSubmissionModalLabel">Form Submission Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="submissionData">
                    <!-- Submission data will be loaded here -->
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p>Loading submission data...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Image View Modal -->
<div class="modal fade" id="imageViewModal" tabindex="-1" aria-labelledby="imageViewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageViewModalLabel">Image Viewer</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center" id="imageViewerContent">
                <img id="modalImage" src="" alt="Full size image" class="img-fluid">
            </div>
            <div class="modal-footer">
                <a id="downloadImageLink" href="" target="_blank" class="btn btn-primary">
                    <i class="bi bi-download"></i> Download
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // View submission button click
        document.querySelectorAll('.view-submission-btn').forEach(button => {
            button.addEventListener('click', function() {
                const submissionId = this.dataset.id;
                loadSubmissionData(submissionId);
            });
        });

        // Delete button click
        document.querySelectorAll('.delete-submission-btn').forEach(button => {
            button.addEventListener('click', function() {
                const submissionId = this.dataset.id;

                if (confirm('Are you sure you want to delete this submission? It will be marked as deleted and hidden from view.')) {
                    toggleSubmissionStatus(submissionId, 'deleted');
                }
            });
        });

        // Set up event delegation for image view buttons that will be added dynamically
        document.addEventListener('click', function(e) {
            // Check if the clicked element is a view button inside the submission data
            if (e.target && e.target.closest('#submissionData a.btn-outline-secondary')) {
                const viewButton = e.target.closest('a.btn-outline-secondary');
                const imageUrl = viewButton.getAttribute('href');

                // If it's an image, show it in the modal instead of opening in a new tab
                if (imageUrl) {
                    e.preventDefault(); // Prevent default link behavior

                    // Set the image source in the modal
                    document.getElementById('modalImage').src = imageUrl;
                    document.getElementById('downloadImageLink').href = imageUrl;

                    // Show the modal
                    const imageModal = new bootstrap.Modal(document.getElementById('imageViewModal'));
                    imageModal.show();
                }
            }
        });

        // Load submission data
        function loadSubmissionData(submissionId) {
            const submissionData = document.getElementById('submissionData');
            submissionData.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading submission data...</p>
                </div>
            `;

            fetch(`{{ url('super-admin/form-builder/submission') }}/${submissionId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        submissionData.innerHTML = `
                            <div class="alert alert-danger">
                                ${data.error}
                            </div>
                        `;
                        return;
                    }

                    // Parse form data
                    let formData = {};
                    try {
                        // Check if form_data is already an object
                        if (typeof data.form_data === 'object' && data.form_data !== null) {
                            formData = data.form_data;
                        } else {
                            // Try to parse it as JSON
                            formData = JSON.parse(data.form_data);
                        }
                    } catch (e) {
                        console.error('Error parsing form data:', e);
                        formData = {};
                    }

                    console.log('Form data:', formData);

                    // Parse form structure
                    let formStructure = [];
                    try {
                        // Check if form_structure is already an array
                        if (Array.isArray(data.form.form_structure)) {
                            formStructure = data.form.form_structure;
                        } else if (typeof data.form.form_structure === 'object' && data.form.form_structure !== null) {
                            // If it's an object but not an array, convert to array
                            formStructure = Object.values(data.form.form_structure);
                        } else {
                            // Try to parse it as JSON
                            formStructure = JSON.parse(data.form.form_structure);
                        }
                    } catch (e) {
                        console.error('Error parsing form structure:', e);
                        formStructure = [];
                    }

                    console.log('Form structure:', formStructure);

                    // Build submission display
                    let html = `
                        <div class="mb-3">
                            <strong>Submitted By:</strong> ${data.name || 'Unknown User'}<br>
                            <strong>Email:</strong> ${data.email || 'N/A'}<br>
                            <strong>Submission Date:</strong> ${data.created_at ? new Date(data.created_at).toLocaleString() : 'N/A'}
                        </div>
                        <hr>
                        <h6>Form Responses:</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Field</th>
                                        <th>Response</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;

                    // Process all fields in the submission
                    for (const [key, value] of Object.entries(data)) {
                        // Skip internal fields and metadata
                        if (['id', 'user_id', 'created_at', 'updated_at', 'form', 'form_id', 'status', 'name', 'email'].includes(key)) {
                            continue;
                        }

                        // Find matching field in form structure if possible
                        const matchingField = formStructure.find(field =>
                            field.label === key ||
                            field.label.toLowerCase().replace(/\s+/g, '_') === key.toLowerCase()
                        );

                        const fieldType = matchingField ? matchingField.type : 'text';
                        const displayKey = matchingField ? matchingField.label : key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

                        html += `
                            <tr>
                                <td><strong>${displayKey}</strong></td>
                                <td>${formatFieldValue(value, fieldType)}</td>
                            </tr>
                        `;
                    }

                    html += `
                                </tbody>
                            </table>
                        </div>
                    `;

                    submissionData.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading submission data:', error);
                    submissionData.innerHTML = `
                        <div class="alert alert-danger">
                            An error occurred while loading the submission data. Please try again later.
                        </div>
                    `;
                });
        }

        // Toggle submission status
        function toggleSubmissionStatus(submissionId, status) {
            fetch(`{{ url('super-admin/form-builder/submission') }}/${submissionId}/toggle-status`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({ status: status })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert(data.error);
                    return;
                }

                const newStatus = data.status;

                // If deleted, remove the row
                if (newStatus === 'deleted') {
                    const row = document.querySelector(`.delete-submission-btn[data-id="${submissionId}"]`).closest('tr');
                    row.remove();

                    // Show success message
                    alert('Submission deleted successfully.');
                } else {
                    // Reload the page to reflect the changes
                    window.location.reload();
                }
            })
            .catch(error => {
                console.error('Error toggling submission status:', error);
                alert('An error occurred while updating the submission status. Please try again later.');
            });
        }

        // Helper function to get appropriate icon for file types
        function getFileIcon(extension) {
            const iconMap = {
                // Documents
                'doc': 'bi bi-file-earmark-word text-primary',
                'docx': 'bi bi-file-earmark-word text-primary',
                'pdf': 'bi bi-file-earmark-pdf text-danger',
                'txt': 'bi bi-file-earmark-text text-secondary',
                'rtf': 'bi bi-file-earmark-text text-secondary',

                // Spreadsheets
                'xls': 'bi bi-file-earmark-excel text-success',
                'xlsx': 'bi bi-file-earmark-excel text-success',
                'csv': 'bi bi-file-earmark-spreadsheet text-success',

                // Presentations
                'ppt': 'bi bi-file-earmark-ppt text-warning',
                'pptx': 'bi bi-file-earmark-ppt text-warning',

                // Archives
                'zip': 'bi bi-file-earmark-zip text-info',
                'rar': 'bi bi-file-earmark-zip text-info',
                '7z': 'bi bi-file-earmark-zip text-info',

                // Images (fallback, though these should be handled separately)
                'jpg': 'bi bi-file-earmark-image text-primary',
                'jpeg': 'bi bi-file-earmark-image text-primary',
                'png': 'bi bi-file-earmark-image text-primary',
                'gif': 'bi bi-file-earmark-image text-primary',
                'svg': 'bi bi-file-earmark-image text-primary',

                // Audio/Video
                'mp3': 'bi bi-file-earmark-music text-purple',
                'mp4': 'bi bi-file-earmark-play text-danger',
                'avi': 'bi bi-file-earmark-play text-danger',
                'mov': 'bi bi-file-earmark-play text-danger',
            };

            return iconMap[extension] || 'bi bi-file-earmark text-muted';
        }

        function formatFieldValue(value, type) {
            if (value === null || value === undefined) {
                return 'No response';
            }

            if (value === '') {
                return '';
            }

            switch (type) {
                case 'file':
                    // If it's a file, show download and view links
                    if (typeof value === 'string' && (
                        value.startsWith('uploads/') ||
                        value.startsWith('form_uploads/') ||
                        value.startsWith('public/') ||
                        value.includes('.')
                    )) {
                        // Clean up the path if needed
                        if (value.startsWith('public/')) {
                            value = value.replace('public/', '');
                        }

                        const fileExtension = value.split('.').pop().toLowerCase();
                        const isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(fileExtension);
                        const isPdf = fileExtension === 'pdf';

                        let viewButton = '';

                        if (isImage) {
                            // For images, show a thumbnail that can be clicked to view
                            return `
                                <div>
                                    <a href="/storage/${value}" target="_blank" class="mb-2 d-block">
                                        <img src="/storage/${value}" alt="Image" class="img-thumbnail"
                                             style="max-width: 200px; max-height: 200px; object-fit: cover;"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="alert alert-warning" style="display: none;">
                                            <small><i class="bi bi-exclamation-triangle"></i> Image not available</small>
                                        </div>
                                    </a>
                                    <div class="btn-group" role="group">
                                        <a href="/storage/${value}" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i> View Full Size
                                        </a>
                                        <a href="/storage/${value}" download class="btn btn-sm btn-outline-secondary">
                                            <i class="bi bi-download"></i> Download
                                        </a>
                                    </div>
                                </div>
                            `;
                        } else if (isPdf) {
                            // For PDFs, offer view in browser option
                            return `
                                <div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="bi bi-file-earmark-pdf text-danger me-2" style="font-size: 1.5rem;"></i>
                                        <span class="text-muted">${value.split('/').pop()}</span>
                                    </div>
                                    <div class="btn-group" role="group">
                                        <a href="/storage/${value}" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i> View in Browser
                                        </a>
                                        <a href="/storage/${value}" download class="btn btn-sm btn-outline-secondary">
                                            <i class="bi bi-download"></i> Download
                                        </a>
                                    </div>
                                </div>
                            `;
                        } else {
                            // For other files, offer both download and view options
                            const fileName = value.split('/').pop();
                            const fileIcon = getFileIcon(fileExtension);
                            return `
                                <div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="${fileIcon} me-2" style="font-size: 1.5rem;"></i>
                                        <span class="text-muted">${fileName}</span>
                                    </div>
                                    <div class="btn-group" role="group">
                                        <a href="/storage/${value}" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i> View
                                        </a>
                                        <a href="/storage/${value}" download class="btn btn-sm btn-outline-secondary">
                                            <i class="bi bi-download"></i> Download
                                        </a>
                                    </div>
                                </div>
                            `;
                        }
                    }
                    return value;

                case 'checkbox':
                    // Handle checkbox values properly
                    if (Array.isArray(value)) {
                        // If it's an array, join the values
                        return value.length > 0 ? value.join(', ') : 'No selection';
                    }

                    // Handle string arrays (JSON encoded)
                    if (typeof value === 'string') {
                        try {
                            const parsed = JSON.parse(value);
                            if (Array.isArray(parsed)) {
                                return parsed.length > 0 ? parsed.join(', ') : 'No selection';
                            }
                        } catch (e) {
                            // Not JSON, treat as regular string
                        }

                        // Handle comma-separated values
                        if (value.includes(',')) {
                            const items = value.split(',').map(item => item.trim()).filter(item => item);
                            return items.length > 0 ? items.join(', ') : 'No selection';
                        }
                    }

                    // Check if this is a checkbox group by looking at the field structure
                    const isCheckboxGroup = matchingField && matchingField.options && matchingField.options.length > 0;

                    if (isCheckboxGroup) {
                        // For checkbox groups, empty/false values mean no selection
                        if (value === false || value === 'false' || value === 0 || value === '0' || value === '' || value === null) {
                            return 'No selection';
                        }
                        // If it's not empty but not an array, treat as single selection
                        return value;
                    } else {
                        // Handle single checkbox (true/false)
                        if (value === true || value === 'true' || value === 1 || value === '1') {
                            return 'Yes';
                        }

                        if (value === false || value === 'false' || value === 0 || value === '0' || value === '' || value === null) {
                            return 'No';
                        }
                    }

                    // Return the value as is if it's something else
                    return value;

                case 'image':
                    // If it's an image, show a thumbnail
                    if (typeof value === 'string' && (
                        value.startsWith('uploads/') ||
                        value.startsWith('form_uploads/') ||
                        value.startsWith('public/') ||
                        value.includes('.')
                    )) {
                        // Clean up the path if needed
                        if (value.startsWith('public/')) {
                            value = value.replace('public/', '');
                        }
                        return `
                            <div>
                                <div class="mb-2">
                                    <img src="/storage/${value}" alt="Image" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                </div>
                                <div>
                                    <a href="/storage/${value}" target="_blank" class="btn btn-sm btn-outline-primary me-1">
                                        <i class="bi bi-download"></i> Download
                                    </a>
                                    <a href="/storage/${value}" target="_blank" class="btn btn-sm btn-outline-secondary">
                                        <i class="bi bi-eye"></i> View
                                    </a>
                                </div>
                            </div>
                        `;
                    }
                    return value;

                default:
                    return value;
            }
        }
    });
</script>
@endpush

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class FormSubmission extends Model
{
    use HasFactory;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'form_id',
        'user_id',
        'form_data',
        'status'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'form_data' => 'array'
    ];

    /**
     * Get all submissions for a specific form.
     *
     * @param int $formId
     * @return \Illuminate\Support\Collection
     */
    public static function getFormSubmissions($formId)
    {
        $form = FormBuilder::findOrFail($formId);
        $formName = $form->form_name;
        $tableName = 'form_' . Str::slug($formName, '_');

        if (!Schema::hasTable($tableName)) {
            return collect([]);
        }

        $submissions = DB::table($tableName)
            ->join('users', 'users.id', '=', $tableName . '.user_id')
            ->select($tableName . '.*', 'users.name', 'users.email')
            ->orderBy($tableName . '.created_at', 'desc')
            ->get();

        // Convert created_at and updated_at to Carbon instances
        foreach ($submissions as $submission) {
            if (isset($submission->created_at)) {
                $submission->created_at = \Carbon\Carbon::parse($submission->created_at);
            }
            if (isset($submission->updated_at)) {
                $submission->updated_at = \Carbon\Carbon::parse($submission->updated_at);
            }
        }

        // Add form relationship to each submission
        $submissions->each(function ($submission) use ($form) {
            $submission->form = $form;
            $submission->form_id = $form->id;
            $submission->status = 'active'; // Default status
        });

        return $submissions;
    }

    /**
     * Get a specific submission by ID.
     *
     * @param int $submissionId
     * @param int $formId
     * @return object|null
     */
    public static function getSubmission($submissionId, $formId)
    {
        $form = FormBuilder::findOrFail($formId);
        $formName = $form->form_name;
        $tableName = 'form_' . Str::slug($formName, '_');

        if (!Schema::hasTable($tableName)) {
            return null;
        }

        $submission = DB::table($tableName)
            ->join('users', 'users.id', '=', $tableName . '.user_id')
            ->select($tableName . '.*', 'users.name', 'users.email')
            ->where($tableName . '.id', $submissionId)
            ->first();

        if (!$submission) {
            return null;
        }

        // Convert created_at and updated_at to Carbon instances
        if (isset($submission->created_at)) {
            $submission->created_at = \Carbon\Carbon::parse($submission->created_at);
        }
        if (isset($submission->updated_at)) {
            $submission->updated_at = \Carbon\Carbon::parse($submission->updated_at);
        }

        // Add form relationship
        $submission->form = $form;
        $submission->form_id = $form->id;
        $submission->status = 'active'; // Default status

        return $submission;
    }

    /**
     * Delete a submission (mark as deleted).
     *
     * @param int $submissionId
     * @param int $formId
     * @return bool
     */
    public static function deleteSubmission($submissionId, $formId)
    {
        $form = FormBuilder::findOrFail($formId);
        $formName = $form->form_name;
        $tableName = 'form_' . Str::slug($formName, '_');

        if (!Schema::hasTable($tableName)) {
            return false;
        }

        // Delete the record from the form-specific table
        $deleted = DB::table($tableName)
            ->where('id', $submissionId)
            ->delete();

        return $deleted > 0;
    }

    /**
     * Get the user that created this submission.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the form that this submission belongs to.
     */
    public function form()
    {
        return $this->belongsTo(FormBuilder::class, 'form_id');
    }

    /**
     * Create a new form submission directly in the form-specific table.
     *
     * @param int $formId
     * @param int $userId
     * @param array $formData
     * @return int|null The ID of the new submission, or null if creation failed
     */
    public static function createSubmission($formId, $userId, $formData)
    {
        try {
            Log::info('Creating form submission', [
                'form_id' => $formId,
                'user_id' => $userId,
                'data_count' => count($formData)
            ]);

            $form = FormBuilder::findOrFail($formId);
            $formName = $form->form_name;
            $tableName = 'form_' . Str::slug($formName, '_');

            Log::info('Form details retrieved', [
                'form_name' => $formName,
                'table_name' => $tableName
            ]);

            // Check if the table exists
            if (!Schema::hasTable($tableName)) {
                Log::error('Form-specific table does not exist', [
                    'table_name' => $tableName
                ]);

                // Create the table if it doesn't exist
                self::createFormTable($form);

                // Check again if the table was created
                if (!Schema::hasTable($tableName)) {
                    Log::error('Failed to create form-specific table', [
                        'table_name' => $tableName
                    ]);
                    return null;
                }

                Log::info('Form-specific table created successfully', [
                    'table_name' => $tableName
                ]);
            }

            // Get the table structure to validate column names
            $columns = Schema::getColumnListing($tableName);
            Log::info('Table columns', [
                'table_name' => $tableName,
                'columns' => $columns
            ]);

            // Prepare data for insertion
            $data = [
                'user_id' => $userId,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Add form data
            foreach ($formData as $key => $value) {
                // Convert field names to column names (slug format)
                $columnName = Str::slug($key, '_');

                // Check if the column exists in the table
                if (in_array($columnName, $columns)) {
                    $data[$columnName] = $value;
                } else {
                    Log::warning("Column '{$columnName}' does not exist in table '{$tableName}'", [
                        'original_key' => $key
                    ]);
                }
            }

            Log::info('Prepared data for insertion', [
                'table_name' => $tableName,
                'data_keys' => array_keys($data)
            ]);

            // Insert the data and get the ID
            $id = DB::table($tableName)->insertGetId($data);

            Log::info('Form submission created successfully', [
                'submission_id' => $id,
                'table_name' => $tableName
            ]);

            return $id;
        } catch (\Exception $e) {
            Log::error('Error creating form submission: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'form_id' => $formId,
                'user_id' => $userId
            ]);
            throw $e; // Re-throw the exception to be caught by the caller
        }
    }

    /**
     * Create a form-specific table based on the form structure.
     *
     * @param FormBuilder $form
     * @return bool
     */
    public static function createFormTable($form)
    {
        try {
            $formName = $form->form_name;
            $tableName = 'form_' . Str::slug($formName, '_');

            // Get form structure - it might already be an array due to the accessor
            $formStructure = $form->form_structure;

            // If it's a string, decode it; if it's already an array, use it directly
            if (is_string($formStructure)) {
                $formStructure = json_decode($formStructure, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::error('Invalid JSON in form structure', [
                        'form_id' => $form->id,
                        'form_name' => $formName,
                        'json_error' => json_last_error_msg()
                    ]);
                    return false;
                }
            }

            if (!is_array($formStructure)) {
                Log::error('Invalid form structure - not an array', [
                    'form_id' => $form->id,
                    'form_name' => $formName,
                    'type' => gettype($formStructure)
                ]);
                return false;
            }

            Schema::create($tableName, function ($table) use ($formStructure) {
                $table->id();
                $table->unsignedBigInteger('user_id');

                // Add columns based on form structure
                foreach ($formStructure as $field) {
                    if (isset($field['label'])) {
                        $columnName = Str::slug($field['label'], '_');

                        // Determine column type based on field type
                        switch ($field['type']) {
                            case 'textarea':
                                $table->text($columnName)->nullable();
                                break;
                            case 'number':
                                // Check if this is a phone number field
                                if (stripos($columnName, 'phone') !== false ||
                                    stripos($field['label'], 'phone') !== false ||
                                    stripos($columnName, 'mobile') !== false ||
                                    stripos($field['label'], 'mobile') !== false) {
                                    // Phone numbers should be stored as strings
                                    $table->string($columnName, 20)->nullable();
                                } else {
                                    // Regular numbers can be integers
                                    $table->bigInteger($columnName)->nullable();
                                }
                                break;
                            case 'date':
                                // Use string for date fields to allow more flexible formats
                                $table->string($columnName, 20)->nullable();
                                break;
                            case 'checkbox':
                                $table->boolean($columnName)->default(false);
                                break;
                            case 'file':
                            case 'image':
                                $table->string($columnName, 255)->nullable();
                                break;
                            case 'email':
                                $table->string($columnName, 255)->nullable();
                                break;
                            case 'tel':
                            case 'phone':
                                // Explicitly handle phone fields
                                $table->string($columnName, 20)->nullable();
                                break;
                            default:
                                $table->string($columnName, 255)->nullable();
                                break;
                        }
                    }
                }

                $table->timestamps();

                // Add foreign key constraint
                $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            });

            Log::info('Form-specific table created', [
                'table_name' => $tableName,
                'form_id' => $form->id
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Error creating form-specific table: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'form_id' => $form->id,
                'form_name' => $form->form_name
            ]);
            return false;
        }
    }
}

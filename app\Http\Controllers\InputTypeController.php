<?php

namespace App\Http\Controllers;

use App\Models\FormBuilder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Carbon\Carbon;

class InputTypeController extends Controller
{
    /**
     * Process text input.
     *
     * @param Request $request
     * @param string $fieldName
     * @return string|null
     */
    public function processTextInput(Request $request, $fieldName)
    {
        return $request->input($fieldName);
    }

    /**
     * Process textarea input.
     *
     * @param Request $request
     * @param string $fieldName
     * @return string|null
     */
    public function processTextareaInput(Request $request, $fieldName)
    {
        return $request->input($fieldName);
    }

    /**
     * Process number input.
     *
     * @param Request $request
     * @param string $fieldName
     * @return int|float|null
     */
    public function processNumberInput(Request $request, $fieldName)
    {
        $value = $request->input($fieldName);
        return is_numeric($value) ? $value : null;
    }

    /**
     * Process email input.
     *
     * @param Request $request
     * @param string $fieldName
     * @return string|null
     */
    public function processEmailInput(Request $request, $fieldName)
    {
        $email = $request->input($fieldName);

        if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return $email;
        }

        return null;
    }

    /**
     * Process date input.
     *
     * @param Request $request
     * @param string $fieldName
     * @return string|null
     */
    public function processDateInput(Request $request, $fieldName)
    {
        $date = $request->input($fieldName);

        if (empty($date)) {
            return null;
        }

        // Log the original date for debugging
        Log::info("Processing date input", [
            'field' => $fieldName,
            'original_date' => $date
        ]);

        // Try different date formats
        try {
            // First try direct parsing with Carbon
            $parsedDate = Carbon::parse($date);
            $formattedDate = $parsedDate->format('Y-m-d');

            Log::info("Successfully parsed date using Carbon::parse", [
                'field' => $fieldName,
                'original' => $date,
                'formatted' => $formattedDate
            ]);

            return $formattedDate;
        } catch (\Exception $e) {
            // If direct parsing fails, try common formats
            $formats = [
                'd/m/Y', // 19/01/2024
                'd-m-Y', // 19-01-2024
                'm/d/Y', // 01/19/2024
                'm-d-Y', // 01-19-2024
                'Y/m/d', // 2024/01/19
                'Y-m-d', // 2024-01-19
                'd.m.Y', // 19.01.2024
                'Y.m.d'  // 2024.01.19
            ];

            foreach ($formats as $format) {
                try {
                    $dateTime = \DateTime::createFromFormat($format, $date);

                    if ($dateTime !== false) {
                        $formattedDate = $dateTime->format('Y-m-d');

                        Log::info("Successfully parsed date using format {$format}", [
                            'field' => $fieldName,
                            'original' => $date,
                            'formatted' => $formattedDate
                        ]);

                        return $formattedDate;
                    }
                } catch (\Exception) {
                    // Continue to the next format
                }
            }

            // If all formats fail, log the error and return null
            Log::warning("Failed to parse date for field {$fieldName}: {$date}", [
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Process checkbox input.
     *
     * @param Request $request
     * @param string $fieldName
     * @return bool
     */
    public function processCheckboxInput(Request $request, $fieldName)
    {
        return $request->has($fieldName) && $request->input($fieldName) === 'on';
    }

    /**
     * Process radio input.
     *
     * @param Request $request
     * @param string $fieldName
     * @return string|null
     */
    public function processRadioInput(Request $request, $fieldName)
    {
        return $request->input($fieldName);
    }

    /**
     * Process select input.
     *
     * @param Request $request
     * @param string $fieldName
     * @return string|array|null
     */
    public function processSelectInput(Request $request, $fieldName)
    {
        return $request->input($fieldName);
    }

    /**
     * Process file input.
     *
     * @param Request $request
     * @param string $fieldName
     * @return string|null Path to the stored file
     */
    public function processFileInput(Request $request, $fieldName)
    {
        if (!$request->hasFile($fieldName)) {
            return null;
        }

        try {
            $file = $request->file($fieldName);
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('form_uploads', $fileName, 'public');

            Log::info("File uploaded successfully", [
                'field' => $fieldName,
                'path' => $filePath
            ]);

            return $filePath;
        } catch (\Exception $e) {
            Log::error("File upload failed for field {$fieldName}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return null;
        }
    }

    /**
     * Process image input.
     *
     * @param Request $request
     * @param string $fieldName
     * @return string|null Path to the stored image
     */
    public function processImageInput(Request $request, $fieldName)
    {
        if (!$request->hasFile($fieldName)) {
            return null;
        }

        try {
            $file = $request->file($fieldName);

            // Validate that it's an image
            $validator = Validator::make(
                [$fieldName => $file],
                [$fieldName => 'image|max:10240'] // 10MB max
            );

            if ($validator->fails()) {
                Log::warning("Invalid image file for field {$fieldName}", [
                    'errors' => $validator->errors()->toArray()
                ]);
                return null;
            }

            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('form_uploads/images', $fileName, 'public');

            Log::info("Image uploaded successfully", [
                'field' => $fieldName,
                'path' => $filePath
            ]);

            return $filePath;
        } catch (\Exception $e) {
            Log::error("Image upload failed for field {$fieldName}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return null;
        }
    }

    /**
     * Process all form inputs based on their types.
     *
     * @param Request $request
     * @param int $formId
     * @return array Processed form data
     */
    public function processFormInputs(Request $request, $formId)
    {
        try {
            $form = FormBuilder::findOrFail($formId);

            // Log the form structure for debugging
            Log::info('Form structure', [
                'form_id' => $formId,
                'form_structure' => $form->form_structure
            ]);

            // Get form structure - it might already be an array due to the accessor
            $formStructure = $form->form_structure;

            // If it's already an array (from the accessor), use it directly
            if (is_array($formStructure)) {
                Log::info('Form structure is already an array from accessor', [
                    'field_count' => count($formStructure)
                ]);
            }
            // If it's a string, try to decode it
            elseif (is_string($formStructure)) {
                Log::info('Form structure is a string, attempting to decode', ['structure' => substr($formStructure, 0, 200)]);

                // First attempt: direct JSON decode
                $decoded = json_decode($formStructure, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                    $formStructure = $decoded;
                } else {
                    // Method 2: Try to extract JSON from string if it's wrapped in something
                    if (preg_match('/\[(.*)\]/s', $formStructure, $matches)) {
                        $extractedJson = '[' . $matches[1] . ']';
                        $decoded = json_decode($extractedJson, true);

                        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                            $formStructure = $decoded;
                        } else {
                            Log::warning('Failed to parse form structure using regex extraction', [
                                'error' => json_last_error_msg()
                            ]);
                        }
                    }

                    // Method 3: Try to parse as a single object
                    if (!is_array($formStructure)) {
                        $decoded = json_decode('[' . $formStructure . ']', true);
                        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                            $formStructure = $decoded;
                        }
                    }
                }
            }

            // Final validation
            if (!is_array($formStructure)) {
                Log::error('Failed to parse form structure after all attempts', [
                    'form_id' => $formId,
                    'form_structure_type' => gettype($form->form_structure),
                    'form_structure_value' => is_string($form->form_structure) ? substr($form->form_structure, 0, 200) : $form->form_structure,
                    'json_error' => json_last_error_msg()
                ]);
                throw new \Exception('Invalid form structure: unable to parse as array');
            }

            Log::info('Parsed form structure', [
                'field_count' => count($formStructure)
            ]);

            $processedData = [];

            // Process all form fields from the request
            foreach (array_keys($request->except(['_token', 'form_id'])) as $key) {
                // Find the field in the form structure
                $field = null;
                foreach ($formStructure as $f) {
                    if (isset($f['label']) && $f['label'] === $key) {
                        $field = $f;
                        break;
                    }
                }

                // If field not found in structure, use a default type
                $fieldType = $field && isset($field['type']) ? $field['type'] : 'text';

                // Process the field based on its type
                switch ($fieldType) {
                    case 'text':
                        $processedData[$key] = $this->processTextInput($request, $key);
                        break;
                    case 'textarea':
                        $processedData[$key] = $this->processTextareaInput($request, $key);
                        break;
                    case 'number':
                        $processedData[$key] = $this->processNumberInput($request, $key);
                        break;
                    case 'email':
                        $processedData[$key] = $this->processEmailInput($request, $key);
                        break;
                    case 'date':
                        $processedData[$key] = $this->processDateInput($request, $key);
                        break;
                    case 'checkbox':
                        $processedData[$key] = $this->processCheckboxInput($request, $key);
                        break;
                    case 'radio':
                        $processedData[$key] = $this->processRadioInput($request, $key);
                        break;
                    case 'select':
                        $processedData[$key] = $this->processSelectInput($request, $key);
                        break;
                    case 'file':
                        $filePath = $this->processFileInput($request, $key);
                        if ($filePath) {
                            $processedData[$key] = $filePath;
                        }
                        break;
                    case 'image':
                        $imagePath = $this->processImageInput($request, $key);
                        if ($imagePath) {
                            $processedData[$key] = $imagePath;
                        }
                        break;
                    default:
                        // For any other type, just get the input value
                        $processedData[$key] = $request->input($key);
                        break;
                }
            }

            // Check for file uploads that might not be in the form structure
            foreach ($request->files as $key => $file) {
                if (!isset($processedData[$key]) && $file) {
                    $processedData[$key] = $this->processFileInput($request, $key);
                }
            }

            Log::info('Processed form data', [
                'processed_fields' => array_keys($processedData)
            ]);

            return $processedData;
        } catch (\Exception $e) {
            Log::error('Error processing form inputs: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'form_id' => $formId
            ]);

            throw $e;
        }
    }

    /**
     * Submit a form with all input types.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function submitForm(Request $request)
    {
        try {
            // Log the request data for debugging
            Log::info('Form submission request received', [
                'request_data' => $request->except(['_token']),
                'user_id' => Auth::id(),
                'content_type' => $request->header('Content-Type')
            ]);

            // Handle different request formats (FormData vs JSON)
            if ($request->has('form_data')) {
                // JSON format: merge form_data fields into the main request
                $formData = $request->input('form_data');
                if (is_array($formData) && !empty($formData)) {
                    $request->merge($formData);
                    Log::info('Merged form_data into request', [
                        'merged_fields' => array_keys($formData)
                    ]);
                } else {
                    Log::warning('form_data is empty or not an array', [
                        'form_data_type' => gettype($formData),
                        'form_data_value' => $formData
                    ]);
                }
            }

            // Validate the request
            $validator = Validator::make($request->all(), [
                'form_id' => 'required|exists:form_builders,id'
            ]);

            if ($validator->fails()) {
                Log::warning('Form submission validation failed', [
                    'errors' => $validator->errors()->toArray()
                ]);

                return response()->json([
                    'error' => 'Validation failed',
                    'messages' => $validator->errors()
                ], 422);
            }

            $formId = $request->input('form_id');

            // Get the form details
            $form = \App\Models\FormBuilder::find($formId);
            if (!$form) {
                Log::error('Form not found', ['form_id' => $formId]);
                return response()->json([
                    'error' => 'Form not found'
                ], 404);
            }

            // Check if the form submission is still allowed (date restriction)
            if ($form->last_date && now() > $form->last_date) {
                Log::warning('Form submission rejected: Past deadline', [
                    'form_id' => $formId,
                    'user_id' => Auth::id(),
                    'last_date' => $form->last_date->format('Y-m-d H:i:s'),
                    'current_time' => now()->format('Y-m-d H:i:s')
                ]);

                return response()->json([
                    'error' => 'This form is no longer accepting submissions. The deadline was ' . $form->last_date->format('M d, Y H:i')
                ], 403);
            }

            // Check if the scientist has already submitted this form
            $tableName = 'form_' . Str::slug($form->form_name, '_');

            // Check if the form-specific table exists
            if (Schema::hasTable($tableName)) {
                // Check if the scientist has already submitted this form
                $hasSubmitted = DB::table($tableName)
                    ->where('user_id', Auth::id())
                    ->exists();

                if ($hasSubmitted) {
                    // Check if the form has been edited after the scientist's submission
                    $submission = DB::table($tableName)
                        ->where('user_id', Auth::id())
                        ->first();

                    if ($submission && $form->last_edited_at) {
                        $submissionDate = new \DateTime($submission->updated_at);
                        $editDate = new \DateTime($form->last_edited_at);

                        // If the form was not edited after the submission, prevent resubmission
                        if ($editDate <= $submissionDate) {
                            Log::warning('Form submission rejected: Already submitted and not edited by admin', [
                                'form_id' => $formId,
                                'user_id' => Auth::id(),
                                'submission_date' => $submissionDate->format('Y-m-d H:i:s'),
                                'last_edited_at' => $editDate ? $editDate->format('Y-m-d H:i:s') : 'null'
                            ]);

                            return response()->json([
                                'error' => 'You have already submitted this form. You can only resubmit if an administrator updates the form.'
                            ], 403);
                        } else {
                            // Form was edited after submission, allow resubmission
                            Log::info('Allowing resubmission of form that was edited by admin', [
                                'form_id' => $formId,
                                'user_id' => Auth::id(),
                                'submission_date' => $submissionDate->format('Y-m-d H:i:s'),
                                'last_edited_at' => $editDate->format('Y-m-d H:i:s')
                            ]);

                            // Delete the previous submission to avoid duplicates
                            DB::table($tableName)
                                ->where('user_id', Auth::id())
                                ->delete();
                        }
                    } else {
                        // No last_edited_at timestamp or no submission found, prevent resubmission
                        Log::warning('Form submission rejected: Already submitted', [
                            'form_id' => $formId,
                            'user_id' => Auth::id()
                        ]);

                        return response()->json([
                            'error' => 'You have already submitted this form. You can only resubmit if an administrator updates the form.'
                        ], 403);
                    }
                }
            }

            // Check if the dynamic table exists for this form
            $tableName = 'form_' . \Illuminate\Support\Str::slug($form->form_name, '_');
            if (!\Illuminate\Support\Facades\Schema::hasTable($tableName)) {
                Log::error('Dynamic table missing for form', [
                    'form_id' => $formId,
                    'form_name' => $form->form_name,
                    'expected_table' => $tableName
                ]);

                return response()->json([
                    'error' => 'Form configuration error: The form table is missing. Please contact the administrator.'
                ], 500);
            }

            Log::info('Processing form inputs', [
                'form_id' => $formId,
                'form_name' => $form->form_name,
                'table_name' => $tableName
            ]);

            // Process all form inputs
            $processedData = $this->processFormInputs($request, $formId);

            Log::info('Processed form data', [
                'processed_data' => $processedData
            ]);

            // Create the form submission
            $submissionId = \App\Models\FormSubmission::createSubmission(
                $formId,
                Auth::id(),
                $processedData
            );

            if (!$submissionId) {
                Log::error('Failed to create form submission', [
                    'form_id' => $formId,
                    'user_id' => Auth::id()
                ]);

                throw new \Exception('Failed to create form submission');
            }

            Log::info('Form submitted successfully', [
                'submission_id' => $submissionId,
                'form_id' => $formId
            ]);

            return response()->json([
                'message' => 'Form submitted successfully',
                'submission_id' => $submissionId
            ]);
        } catch (\Exception $e) {
            Log::error('Error submitting form: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'form_id' => $request->input('form_id', 'unknown'),
                'user_id' => Auth::id()
            ]);

            // Provide more specific error messages based on the exception type
            $errorMessage = 'An unexpected error occurred while submitting the form.';

            if (strpos($e->getMessage(), 'Table') !== false && strpos($e->getMessage(), "doesn't exist") !== false) {
                $errorMessage = 'Form configuration error: The form table is missing. Please contact the administrator.';
            } elseif (strpos($e->getMessage(), 'Column') !== false && strpos($e->getMessage(), 'not found') !== false) {
                $errorMessage = 'Form configuration error: Form structure mismatch. Please contact the administrator.';
            } elseif (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                $errorMessage = 'You have already submitted this form.';
            } elseif (strpos($e->getMessage(), 'Connection refused') !== false) {
                $errorMessage = 'Database connection error. Please try again later.';
            }

            return response()->json([
                'error' => $errorMessage,
                'debug_message' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }
}

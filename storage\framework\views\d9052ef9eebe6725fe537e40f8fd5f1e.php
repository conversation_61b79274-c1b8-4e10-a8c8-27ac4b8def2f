<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('Available Forms')); ?></h5>
                    <a href="<?php echo e(route('scientist.dashboard')); ?>" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>Below are the forms available for you to fill out based on your district assignment.</li>
                            <li>Click on a form to view and fill it out.</li>
                            <li>Your submissions will be saved and can be reviewed by administrators.</li>
                        </ul>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Form Name</th>
                                    <th>Fields</th>
                                    <th>Last Updated</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="formsTableBody">
                                <!-- Forms will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const formsTableBody = document.getElementById('formsTableBody');

        // Load forms
        loadForms();

        // Load forms function
        function loadForms() {
            fetch('<?php echo e(route("scientist.get-forms")); ?>')
                .then(response => response.json())
                .then(data => {
                    formsTableBody.innerHTML = '';

                    if (data.length === 0) {
                        formsTableBody.innerHTML = '<tr><td colspan="4" class="text-center">No forms available for you at this time.</td></tr>';
                        return;
                    }

                    data.forEach(form => {
                        const row = document.createElement('tr');

                        // Parse form structure - handle both string and array cases
                        let formStructure = [];
                        try {
                            if (typeof form.form_structure === 'string') {
                                // It's a JSON string, parse it
                                formStructure = JSON.parse(form.form_structure);
                            } else if (Array.isArray(form.form_structure)) {
                                // It's already an array (from the backend accessor)
                                formStructure = form.form_structure;
                            } else {
                                console.warn('Unexpected form_structure type:', typeof form.form_structure);
                                formStructure = [];
                            }
                        } catch (e) {
                            console.error('Error parsing form structure:', e);
                            formStructure = [];
                        }

                        // Format date
                        const updatedDate = new Date(form.updated_at);
                        const formattedDate = updatedDate.toLocaleDateString() + ' ' + updatedDate.toLocaleTimeString();

                        // Determine button state based on submission status and deadline
                        let buttonHtml = '';
                        let deadlineInfo = '';

                        // Check if form is past deadline
                        if (form.is_past_deadline) {
                            buttonHtml = `
                                <button class="btn btn-sm btn-danger" disabled>
                                    <i class="bi bi-clock"></i> Deadline Passed
                                </button>
                            `;
                            if (form.last_date) {
                                const deadline = new Date(form.last_date);
                                deadlineInfo = `<div class="small text-danger mt-1">Deadline: ${deadline.toLocaleDateString()} ${deadline.toLocaleTimeString()}</div>`;
                            }
                        } else if (form.already_submitted && !form.can_resubmit) {
                            // Form already submitted and cannot be resubmitted
                            buttonHtml = `
                                <button class="btn btn-sm btn-secondary" disabled>
                                    <i class="bi bi-check-circle"></i> Already Submitted
                                </button>
                            `;
                        } else if (form.already_submitted && form.can_resubmit) {
                            // Form already submitted but can be resubmitted (form was edited)
                            buttonHtml = `
                                <a href="/scientist/forms/${form.id}/read" class="btn btn-sm btn-warning">
                                    <i class="bi bi-pencil"></i> Update Submission
                                </a>
                                <div class="small text-muted mt-1">Form has been updated by admin</div>
                            `;
                            if (form.last_date) {
                                const deadline = new Date(form.last_date);
                                deadlineInfo = `<div class="small text-warning mt-1">Deadline: ${deadline.toLocaleDateString()} ${deadline.toLocaleTimeString()}</div>`;
                            }
                        } else {
                            // Form not submitted yet
                            buttonHtml = `
                                <a href="/scientist/forms/${form.id}/read" class="btn btn-sm btn-primary">
                                    <i class="bi bi-pencil"></i> Fill Form
                                </a>
                            `;
                            if (form.last_date) {
                                const deadline = new Date(form.last_date);
                                deadlineInfo = `<div class="small text-info mt-1">Deadline: ${deadline.toLocaleDateString()} ${deadline.toLocaleTimeString()}</div>`;
                            }
                        }

                        row.innerHTML = `
                            <td>${form.form_name}</td>
                            <td>${formStructure.length} fields</td>
                            <td>${formattedDate}</td>
                            <td>
                                ${buttonHtml}
                                ${deadlineInfo}
                            </td>
                        `;

                        formsTableBody.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error('Error loading forms:', error);
                    formsTableBody.innerHTML = '<tr><td colspan="4" class="text-center text-danger">Error loading forms. Please try again later.</td></tr>';
                });
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\csb\resources\views/scientist/forms/index.blade.php ENDPATH**/ ?>
<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header"><?php echo e(__('Zonal Coordinator Dashboard')); ?></div>

                <div class="card-body">
                    <h2 class="text-center mb-4">Welcome Zonal Coordinator</h2>

                    <div class="alert alert-success" role="alert">
                        You are logged in as a Zonal Coordinator!
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mt-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white mb-3">
                                <div class="card-body">
                                    <h5 class="card-title">Districts</h5>
                                    <p class="card-text display-4"><?php echo e($stats['districts_count'] ?? 0); ?></p>
                                    <p class="card-text">
                                        <small>
                                            Pre-Cocoon: <?php echo e($stats['pre_cocoon_districts'] ?? 0); ?> |
                                            Post-Cocoon: <?php echo e($stats['post_cocoon_districts'] ?? 0); ?>

                                        </small>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white mb-3">
                                <div class="card-body">
                                    <h5 class="card-title">Scientists</h5>
                                    <p class="card-text display-4"><?php echo e($stats['scientists_count'] ?? 0); ?></p>
                                    <p class="card-text">
                                        <small>
                                            Feedback Provided: <?php echo e($stats['feedback_count'] ?? 0); ?>

                                        </small>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white mb-3">
                                <div class="card-body">
                                    <h5 class="card-title">Action Plans</h5>
                                    <p class="card-text display-4"><?php echo e($stats['action_plans_count'] ?? 0); ?></p>
                                    <p class="card-text">
                                        <small>
                                            Completed: <?php echo e($stats['completed_action_plans'] ?? 0); ?>

                                        </small>
                                    </p>
                                </div>
                            </div>
                        </div>
                        

                    </div>

                    <!-- Quick Access Cards -->
                    <div class="row mt-4">
                        <div class="col-md-3">
                            <div class="card mb-3">
                                <div class="card-header">My Districts</div>
                                <div class="card-body">
                                    <p>View and manage your assigned districts.</p>
                                    <a href="<?php echo e(route('zonal-coordinator.districts.index')); ?>" class="btn btn-primary">View Districts</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card mb-3">
                                <div class="card-header">Scientists</div>
                                <div class="card-body">
                                    <p>View scientists in your assigned districts.</p>
                                    <a href="<?php echo e(route('zonal-coordinator.scientists.index')); ?>" class="btn btn-primary">View Scientists</a>
                                </div>
                            </div>
                        </div>
                        

                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-header">Participant Feedback</div>
                                <div class="card-body">
                                    <p>Collect and manage feedback from event participants.</p>
                                    <div class="d-flex gap-2">
                                        <a href="<?php echo e(route('zonal-coordinator.feedback-import.index')); ?>" class="btn btn-primary me-2">Manage Feedback</a>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card mb-3">
                                <div class="card-header">Scientist & Action Plan Feedback</div>
                                <div class="card-body">
                                    <p>Provide feedback to scientists & Action plans.</p>
                                    <a href="<?php echo e(route('zonal-coordinator.scientist-feedback.index')); ?>" class="btn btn-primary">Manage Feedback</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Action Plans -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">Recent Action Plans</div>
                                <div class="card-body">
                                    <?php if(isset($recentActionPlans) && count($recentActionPlans) > 0): ?>
                                        <div class="table-responsive">
                                            <table class="table table-striped table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Title</th>
                                                        <th>Scientist</th>
                                                        <th>District</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php $__currentLoopData = $recentActionPlans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <tr>
                                                            <td><?php echo e($plan->title); ?></td>
                                                            <td><?php echo e($plan->scientist->name ?? 'N/A'); ?></td>
                                                            <td><?php echo e($plan->district->district ?? 'N/A'); ?></td>
                                                            <td>
                                                                <?php if($plan->status == 'planned'): ?>
                                                                    <span class="badge bg-warning">Planned</span>
                                                                <?php elseif($plan->status == 'completed'): ?>
                                                                    <span class="badge bg-success">Completed</span>
                                                                <?php else: ?>
                                                                    <span class="badge bg-danger">Cancelled</span>
                                                                <?php endif; ?>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </tbody>
                                            </table>
                                        </div>
                                        <a href="<?php echo e(route('zonal-coordinator.events.index')); ?>" class="btn btn-sm btn-primary">View All Events</a>
                                    <?php else: ?>
                                        <p class="text-center">No recent action plans found.</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Visits -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">Your Recent Visits</div>
                                <div class="card-body">
                                    <?php if(isset($recentVisits) && count($recentVisits) > 0): ?>
                                        <div class="table-responsive">
                                            <table class="table table-striped table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Title</th>
                                                        <th>District</th>
                                                        <th>Date</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php $__currentLoopData = $recentVisits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $visit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <tr>
                                                            <td><?php echo e($visit->title); ?></td>
                                                            <td><?php echo e($visit->district->district ?? 'N/A'); ?></td>
                                                            <td><?php echo e(\Carbon\Carbon::parse($visit->visit_date)->format('d M Y')); ?></td>
                                                            <td>
                                                                <?php if($visit->status == 'planned'): ?>
                                                                    <span class="badge bg-warning">Planned</span>
                                                                <?php elseif($visit->status == 'completed'): ?>
                                                                    <span class="badge bg-success">Completed</span>
                                                                <?php else: ?>
                                                                    <span class="badge bg-danger">Cancelled</span>
                                                                <?php endif; ?>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </tbody>
                                            </table>
                                        </div>
                                        <a href="<?php echo e(route('zonal-coordinator.visits.index')); ?>" class="btn btn-sm btn-primary">View All Visits</a>
                                    <?php else: ?>
                                        <p class="text-center">No recent visits found.</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Participant Feedback Management -->
                    <div class="row mt-4">
                        
                                    </div>
                                </div>
                            </div>
                        </div> --}}

                        
                    </div>

                    
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\csb\resources\views/zonal-coordinator/dashboard.blade.php ENDPATH**/ ?>
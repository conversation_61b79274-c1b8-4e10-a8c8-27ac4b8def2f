<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\FormBuilder;
use App\Models\FormSubmission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CustomFormBuilderController extends Controller
{
    /**
     * Display the form builder page.
     */
    public function index()
    {
        return view('super-admin.form-builder.index');
    }

    /**
     * Get all forms.
     */
    public function getForms()
    {
        try {
            $forms = FormBuilder::all();
            return response()->json($forms);
        } catch (\Exception $e) {
            Log::error('Error getting forms: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get forms'], 500);
        }
    }

    /**
     * Get form details.
     */
    public function getFormDetails($id)
    {
        try {
            $form = FormBuilder::findOrFail($id);
            return response()->json($form);
        } catch (\Exception $e) {
            Log::error('Error getting form details: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get form details'], 500);
        }
    }

    /**
     * Create a new form.
     */
    public function createForm(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'form_name' => 'required|string|max:255|unique:form_builders,form_name',
            'target_user' => 'required|string|in:all_scientists,pre_cocoon,post_cocoon',
            'form_structure' => 'required',
            'last_date' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Handle form_structure - it might be a string (JSON) or already an array
            if (is_string($request->form_structure)) {
                // Check for empty string first
                if (trim($request->form_structure) === '') {
                    Log::error('Empty form structure provided', [
                        'form_name' => $request->form_name
                    ]);
                    return response()->json(['error' => 'Form structure cannot be empty'], 422);
                }

                // Clear any previous JSON errors
                json_decode('{}');

                $formStructure = json_decode($request->form_structure, true);
                $jsonError = json_last_error();
                $jsonErrorMsg = json_last_error_msg();

                if ($jsonError !== JSON_ERROR_NONE) {
                    Log::error('Invalid JSON structure provided', [
                        'form_name' => $request->form_name,
                        'json_error' => $jsonErrorMsg,
                        'json_error_code' => $jsonError,
                        'form_structure_preview' => substr($request->form_structure, 0, 200)
                    ]);

                    // Provide a more descriptive error message if json_last_error_msg() is not helpful
                    $errorMessage = ($jsonErrorMsg === 'No error' || empty($jsonErrorMsg))
                        ? 'Invalid JSON syntax in form structure'
                        : $jsonErrorMsg;

                    return response()->json(['error' => 'Invalid form structure JSON: ' . $errorMessage], 422);
                }

                // Additional check: if JSON parsing succeeded but result is null, it might be empty or invalid
                if ($formStructure === null && trim($request->form_structure) !== 'null') {
                    Log::error('JSON parsing returned null for non-null input', [
                        'form_name' => $request->form_name,
                        'form_structure_preview' => substr($request->form_structure, 0, 200),
                        'form_structure_length' => strlen($request->form_structure)
                    ]);
                    return response()->json(['error' => 'Invalid form structure: JSON parsing resulted in null'], 422);
                }

                // Check if the result is still a string (double-encoded JSON)
                if (is_string($formStructure)) {
                    Log::info('Detected double-encoded JSON, attempting second decode');
                    $secondDecode = json_decode($formStructure, true);
                    if (json_last_error() === JSON_ERROR_NONE && is_array($secondDecode)) {
                        $formStructure = $secondDecode;
                        Log::info('Successfully decoded double-encoded JSON');
                    } else {
                        Log::warning('Second decode failed, using original decode result');
                        // If second decode failed and we still have a string, it's invalid
                        if (is_string($formStructure)) {
                            Log::error('Form structure is still a string after decode attempts', [
                                'form_name' => $request->form_name,
                                'form_structure_type' => gettype($formStructure),
                                'form_structure_preview' => substr($formStructure, 0, 200)
                            ]);
                            return response()->json(['error' => 'Form structure must be a valid JSON array, not a string'], 422);
                        }
                    }
                }

                // Additional validation for the decoded result
                if (!is_array($formStructure)) {
                    Log::error('Form structure is not an array after JSON decode', [
                        'form_name' => $request->form_name,
                        'form_structure_type' => gettype($formStructure),
                        'form_structure_value' => $formStructure
                    ]);
                    return response()->json(['error' => 'Form structure must be a JSON array, got ' . gettype($formStructure)], 422);
                }

                $formStructureJson = json_encode($formStructure);
            } else {
                // form_structure is already an array (from JSON request)
                $formStructure = $request->form_structure;

                // Validate that it's actually an array
                if (!is_array($formStructure)) {
                    Log::error('Form structure is not an array', [
                        'form_name' => $request->form_name,
                        'form_structure_type' => gettype($formStructure),
                        'form_structure_value' => $formStructure
                    ]);
                    return response()->json(['error' => 'Form structure must be an array, got ' . gettype($formStructure)], 422);
                }

                $formStructureJson = json_encode($formStructure);
            }

            // Validate that form structure is not empty and has valid fields
            if (empty($formStructure)) {
                Log::error('Form structure is empty', [
                    'form_name' => $request->form_name
                ]);
                return response()->json(['error' => 'Form structure cannot be empty'], 422);
            }

            // Validate each field has required properties
            foreach ($formStructure as $index => $field) {
                if (!is_array($field)) {
                    Log::error('Form field is not an array', [
                        'form_name' => $request->form_name,
                        'field_index' => $index,
                        'field_type' => gettype($field),
                        'field_value' => $field
                    ]);
                    return response()->json([
                        'error' => "Invalid field at position {$index}: Field must be an object/array, got " . gettype($field)
                    ], 422);
                }

                if (!isset($field['type'])) {
                    Log::error('Form field missing type property', [
                        'form_name' => $request->form_name,
                        'field_index' => $index,
                        'field_keys' => array_keys($field)
                    ]);
                    return response()->json([
                        'error' => "Invalid field at position {$index}: Field must have a 'type' property"
                    ], 422);
                }

                if (!isset($field['label'])) {
                    Log::error('Form field missing label property', [
                        'form_name' => $request->form_name,
                        'field_index' => $index,
                        'field_keys' => array_keys($field)
                    ]);
                    return response()->json([
                        'error' => "Invalid field at position {$index}: Field must have a 'label' property"
                    ], 422);
                }
            }

            $form = FormBuilder::create([
                'form_name' => $request->form_name,
                'target_user' => $request->target_user,
                'form_structure' => $formStructureJson,
                'last_date' => $request->last_date,
            ]);

            // Create form-specific table for storing responses
            $this->createFormTable($form);

            Log::info('Form created successfully', [
                'form_id' => $form->id,
                'form_name' => $form->form_name,
                'target_user' => $form->target_user
            ]);

            return response()->json(['message' => 'Form created successfully', 'form' => $form]);
        } catch (\Exception $e) {
            Log::error('Error creating form: ' . $e->getMessage(), [
                'form_name' => $request->form_name,
                'exception' => $e
            ]);
            return response()->json(['error' => 'Failed to create form: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Update an existing form.
     */
    public function updateForm(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'form_name' => 'required|string|max:255|unique:form_builders,form_name,' . $id,
            'target_user' => 'required|string|in:all_scientists,pre_cocoon,post_cocoon',
            'form_structure' => 'required',
            'last_date' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $form = FormBuilder::findOrFail($id);

            // Handle form_structure - it might be a string (JSON) or already an array
            if (is_string($request->form_structure)) {
                // Check for empty string first
                if (trim($request->form_structure) === '') {
                    Log::error('Empty form structure provided for update', [
                        'form_id' => $id,
                        'form_name' => $request->form_name
                    ]);
                    return response()->json(['error' => 'Form structure cannot be empty'], 422);
                }

                // Clear any previous JSON errors
                json_decode('{}');

                $formStructure = json_decode($request->form_structure, true);
                $jsonError = json_last_error();
                $jsonErrorMsg = json_last_error_msg();

                if ($jsonError !== JSON_ERROR_NONE) {
                    Log::error('Invalid JSON structure provided for update', [
                        'form_id' => $id,
                        'form_name' => $request->form_name,
                        'json_error' => $jsonErrorMsg,
                        'json_error_code' => $jsonError,
                        'form_structure_preview' => substr($request->form_structure, 0, 200)
                    ]);

                    // Provide a more descriptive error message if json_last_error_msg() is not helpful
                    $errorMessage = ($jsonErrorMsg === 'No error' || empty($jsonErrorMsg))
                        ? 'Invalid JSON syntax in form structure'
                        : $jsonErrorMsg;

                    return response()->json(['error' => 'Invalid form structure JSON: ' . $errorMessage], 422);
                }

                // Additional check: if JSON parsing succeeded but result is null, it might be empty or invalid
                if ($formStructure === null && trim($request->form_structure) !== 'null') {
                    Log::error('JSON parsing returned null for non-null input in update', [
                        'form_id' => $id,
                        'form_name' => $request->form_name,
                        'form_structure_preview' => substr($request->form_structure, 0, 200),
                        'form_structure_length' => strlen($request->form_structure)
                    ]);
                    return response()->json(['error' => 'Invalid form structure: JSON parsing resulted in null'], 422);
                }

                // Check if the result is still a string (double-encoded JSON)
                if (is_string($formStructure)) {
                    Log::info('Detected double-encoded JSON in update, attempting second decode');
                    $secondDecode = json_decode($formStructure, true);
                    if (json_last_error() === JSON_ERROR_NONE && is_array($secondDecode)) {
                        $formStructure = $secondDecode;
                        Log::info('Successfully decoded double-encoded JSON in update');
                    } else {
                        Log::warning('Second decode failed in update, using original decode result');
                        // If second decode failed and we still have a string, it's invalid
                        if (is_string($formStructure)) {
                            Log::error('Form structure is still a string after decode attempts in update', [
                                'form_id' => $id,
                                'form_name' => $request->form_name,
                                'form_structure_type' => gettype($formStructure),
                                'form_structure_preview' => substr($formStructure, 0, 200)
                            ]);
                            return response()->json(['error' => 'Form structure must be a valid JSON array, not a string'], 422);
                        }
                    }
                }

                // Additional validation for the decoded result
                if (!is_array($formStructure)) {
                    Log::error('Form structure is not an array after JSON decode in update', [
                        'form_id' => $id,
                        'form_name' => $request->form_name,
                        'form_structure_type' => gettype($formStructure),
                        'form_structure_value' => $formStructure
                    ]);
                    return response()->json(['error' => 'Form structure must be a JSON array, got ' . gettype($formStructure)], 422);
                }

                $formStructureJson = json_encode($formStructure);
            } else {
                // form_structure is already an array (from JSON request)
                $formStructure = $request->form_structure;

                // Validate that it's actually an array
                if (!is_array($formStructure)) {
                    Log::error('Form structure is not an array in update', [
                        'form_id' => $id,
                        'form_name' => $request->form_name,
                        'form_structure_type' => gettype($formStructure),
                        'form_structure_value' => $formStructure
                    ]);
                    return response()->json(['error' => 'Form structure must be an array, got ' . gettype($formStructure)], 422);
                }

                $formStructureJson = json_encode($formStructure);
            }

            // Validate that form structure is not empty and has valid fields
            if (empty($formStructure)) {
                Log::error('Form structure is empty in update', [
                    'form_id' => $id,
                    'form_name' => $request->form_name
                ]);
                return response()->json(['error' => 'Form structure cannot be empty'], 422);
            }

            // Validate each field has required properties
            foreach ($formStructure as $index => $field) {
                if (!is_array($field)) {
                    Log::error('Form field is not an array in update', [
                        'form_id' => $id,
                        'form_name' => $request->form_name,
                        'field_index' => $index,
                        'field_type' => gettype($field),
                        'field_value' => $field
                    ]);
                    return response()->json([
                        'error' => "Invalid field at position {$index}: Field must be an object/array, got " . gettype($field)
                    ], 422);
                }

                if (!isset($field['type'])) {
                    Log::error('Form field missing type property in update', [
                        'form_id' => $id,
                        'form_name' => $request->form_name,
                        'field_index' => $index,
                        'field_keys' => array_keys($field)
                    ]);
                    return response()->json([
                        'error' => "Invalid field at position {$index}: Field must have a 'type' property"
                    ], 422);
                }

                if (!isset($field['label'])) {
                    Log::error('Form field missing label property in update', [
                        'form_id' => $id,
                        'form_name' => $request->form_name,
                        'field_index' => $index,
                        'field_keys' => array_keys($field)
                    ]);
                    return response()->json([
                        'error' => "Invalid field at position {$index}: Field must have a 'label' property"
                    ], 422);
                }
            }

            $form->update([
                'form_name' => $request->form_name,
                'target_user' => $request->target_user,
                'form_structure' => $formStructureJson,
                'last_date' => $request->last_date,
                'last_edited_at' => now(), // Set the last_edited_at timestamp
            ]);

            Log::info('Form updated successfully', [
                'form_id' => $form->id,
                'form_name' => $form->form_name,
                'target_user' => $form->target_user
            ]);

            return response()->json(['message' => 'Form updated successfully', 'form' => $form]);
        } catch (\Exception $e) {
            Log::error('Error updating form: ' . $e->getMessage(), [
                'form_id' => $id,
                'exception' => $e
            ]);
            return response()->json(['error' => 'Failed to update form: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Delete a form.
     */
    public function deleteForm($id)
    {
        try {
            $form = FormBuilder::findOrFail($id);
            $form->delete();

            return response()->json(['message' => 'Form deleted successfully']);
        } catch (\Exception $e) {
            Log::error('Error deleting form: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete form'], 500);
        }
    }

    /**
     * Get submission count for a form.
     */
    public function getSubmissionCount($formId)
    {
        try {
            $form = FormBuilder::findOrFail($formId);
            $formName = $form->form_name;
            $tableName = 'form_' . Str::slug($formName, '_');

            // Check if the form-specific table exists
            if (!Schema::hasTable($tableName)) {
                return response()->json(['count' => 0]);
            }

            // Count submissions from the form-specific table
            $count = DB::table($tableName)->count();
            return response()->json(['count' => $count]);
        } catch (\Exception $e) {
            Log::error('Error getting submission count: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['count' => 0]);
        }
    }

    /**
     * View form submissions.
     */
    public function viewSubmissions($formId)
    {
        try {
            $form = FormBuilder::findOrFail($formId);
            $formName = $form->form_name;

            // Create a table name from the form name (sanitized)
            $tableName = 'form_' . Str::slug($formName, '_');

            // Get submissions directly from the form-specific table
            $submissions = FormSubmission::getFormSubmissions($formId);

            return view('super-admin.form-builder.submissions', [
                'form' => $form,
                'submissions' => $submissions,
                'tableName' => $tableName
            ]);
        } catch (\Exception $e) {
            Log::error('Error viewing submissions: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            return redirect()->route('super-admin.form-builder.index')
                ->with('error', 'Failed to load form submissions: ' . $e->getMessage());
        }
    }

    /**
     * Get submission details.
     */
    public function getSubmissionDetails($submissionId)
    {
        try {
            // Extract form_id from the URL
            $url = request()->headers->get('referer');
            $formId = null;

            if (preg_match('/\/submissions\/(\d+)/', $url, $matches)) {
                $formId = $matches[1];
            }

            if (!$formId) {
                return response()->json(['error' => 'Form ID not found'], 400);
            }

            // Get the submission directly from the form-specific table
            $submission = FormSubmission::getSubmission($submissionId, $formId);

            if (!$submission) {
                return response()->json(['error' => 'Submission not found'], 404);
            }

            return response()->json($submission);
        } catch (\Exception $e) {
            Log::error('Error getting submission details: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to get submission details: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Delete a submission.
     */
    public function toggleSubmissionStatus($submissionId)
    {
        try {
            // Extract form_id from the URL
            $url = request()->headers->get('referer');
            $formId = null;

            if (preg_match('/\/submissions\/(\d+)/', $url, $matches)) {
                $formId = $matches[1];
            }

            if (!$formId) {
                return response()->json(['error' => 'Form ID not found'], 400);
            }

            // Delete the submission
            $deleted = FormSubmission::deleteSubmission($submissionId, $formId);

            if (!$deleted) {
                return response()->json(['error' => 'Failed to delete submission'], 404);
            }

            return response()->json([
                'message' => 'Submission deleted successfully',
                'status' => 'deleted'
            ]);
        } catch (\Exception $e) {
            Log::error('Error deleting submission: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to delete submission: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Create predefined forms.
     */
    public function createPredefinedForms()
    {
        try {
            // Create Scientist Profile Form
            $this->createPredefinedForm(
                'Scientist Profile Form',
                $this->getScientistProfileFormStructure()
            );

            // Create District Profile Form (Pre-Cocoon)
            $this->createPredefinedForm(
                'District Profile Form (Pre-Cocoon)',
                $this->getDistrictPreCocoonFormStructure()
            );

            // Create District Profile Form (Post-Cocoon)
            $this->createPredefinedForm(
                'District Profile Form (Post-Cocoon)',
                $this->getDistrictPostCocoonFormStructure()
            );

            // Create Sericulture Profile Form (Pre-Cocoon)
            $this->createPredefinedForm(
                'Sericulture Profile Form (Pre-Cocoon)',
                $this->getSericulturePreCocoonFormStructure()
            );

            // Create Sericulture Profile Form (Post-Cocoon)
            $this->createPredefinedForm(
                'Sericulture Profile Form (Post-Cocoon)',
                $this->getSericulturePostCocoonFormStructure()
            );

            return response()->json(['message' => 'Predefined forms created successfully']);
        } catch (\Exception $e) {
            Log::error('Error creating predefined forms: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to create predefined forms'], 500);
        }
    }

    /**
     * Create a predefined form.
     */
    private function createPredefinedForm($formName, $formStructure)
    {
        // Determine target user based on form name
        $targetUser = 'all_scientists';
        if (strpos($formName, 'Pre-Cocoon') !== false) {
            $targetUser = 'pre_cocoon';
        } elseif (strpos($formName, 'Post-Cocoon') !== false) {
            $targetUser = 'post_cocoon';
        }

        // Check if the form already exists
        $existingForm = FormBuilder::where('form_name', $formName)->first();

        if ($existingForm) {
            // Update the existing form
            $existingForm->update([
                'target_user' => $targetUser,
                'form_structure' => json_encode($formStructure),
                'last_edited_at' => now(), // Set the last_edited_at timestamp for predefined forms too
            ]);
        } else {
            // Create a new form
            FormBuilder::create([
                'form_name' => $formName,
                'target_user' => $targetUser,
                'form_structure' => json_encode($formStructure),
            ]);
        }
    }

    /**
     * Get the structure for the Scientist Profile Form.
     */
    private function getScientistProfileFormStructure()
    {
        return [
            [
                'type' => 'text',
                'label' => 'Full Name',
                'required' => true,
                'placeholder' => 'Enter your full name',
            ],
            [
                'type' => 'email',
                'label' => 'Email Address',
                'required' => true,
                'placeholder' => 'Enter your email address',
            ],
            [
                'type' => 'text',
                'label' => 'Phone Number',
                'required' => true,
                'placeholder' => 'Enter your phone number',
            ],
            [
                'type' => 'text',
                'label' => 'Designation',
                'required' => true,
                'placeholder' => 'Enter your designation',
            ],
            [
                'type' => 'textarea',
                'label' => 'Educational Qualifications',
                'required' => true,
                'placeholder' => 'Enter your educational qualifications',
            ],
            [
                'type' => 'textarea',
                'label' => 'Areas of Expertise',
                'required' => true,
                'placeholder' => 'Enter your areas of expertise',
            ],
            [
                'type' => 'textarea',
                'label' => 'Research Experience',
                'required' => false,
                'placeholder' => 'Enter your research experience',
            ],
            [
                'type' => 'textarea',
                'label' => 'Publications',
                'required' => false,
                'placeholder' => 'Enter your publications',
            ],
        ];
    }

    /**
     * Get the structure for the District Profile Form (Pre-Cocoon).
     */
    private function getDistrictPreCocoonFormStructure()
    {
        return [
            [
                'type' => 'text',
                'label' => 'District Name',
                'required' => true,
                'placeholder' => 'Enter district name',
            ],
            [
                'type' => 'text',
                'label' => 'State',
                'required' => true,
                'placeholder' => 'Enter state name',
            ],
            [
                'type' => 'number',
                'label' => 'Total Area (in sq. km)',
                'required' => true,
                'placeholder' => 'Enter total area',
            ],
            [
                'type' => 'number',
                'label' => 'Population',
                'required' => true,
                'placeholder' => 'Enter population',
            ],
            [
                'type' => 'number',
                'label' => 'Number of Mulberry Farmers',
                'required' => true,
                'placeholder' => 'Enter number of mulberry farmers',
            ],
            [
                'type' => 'number',
                'label' => 'Area Under Mulberry Cultivation (in hectares)',
                'required' => true,
                'placeholder' => 'Enter area under mulberry cultivation',
            ],
            [
                'type' => 'number',
                'label' => 'Annual Mulberry Production (in MT)',
                'required' => true,
                'placeholder' => 'Enter annual mulberry production',
            ],
            [
                'type' => 'number',
                'label' => 'Number of Silkworm Rearers',
                'required' => true,
                'placeholder' => 'Enter number of silkworm rearers',
            ],
            [
                'type' => 'number',
                'label' => 'Annual Cocoon Production (in MT)',
                'required' => true,
                'placeholder' => 'Enter annual cocoon production',
            ],
            [
                'type' => 'textarea',
                'label' => 'Major Challenges in Pre-Cocoon Activities',
                'required' => true,
                'placeholder' => 'Enter major challenges',
            ],
            [
                'type' => 'textarea',
                'label' => 'Government Schemes Available',
                'required' => true,
                'placeholder' => 'Enter government schemes available',
            ],
        ];
    }

    /**
     * Get the structure for the District Profile Form (Post-Cocoon).
     */
    private function getDistrictPostCocoonFormStructure()
    {
        return [
            [
                'type' => 'text',
                'label' => 'District Name',
                'required' => true,
                'placeholder' => 'Enter district name',
            ],
            [
                'type' => 'text',
                'label' => 'State',
                'required' => true,
                'placeholder' => 'Enter state name',
            ],
            [
                'type' => 'number',
                'label' => 'Number of Reeling Units',
                'required' => true,
                'placeholder' => 'Enter number of reeling units',
            ],
            [
                'type' => 'number',
                'label' => 'Number of Weaving Units',
                'required' => true,
                'placeholder' => 'Enter number of weaving units',
            ],
            [
                'type' => 'number',
                'label' => 'Annual Raw Silk Production (in MT)',
                'required' => true,
                'placeholder' => 'Enter annual raw silk production',
            ],
            [
                'type' => 'number',
                'label' => 'Annual Silk Fabric Production (in sq. meters)',
                'required' => true,
                'placeholder' => 'Enter annual silk fabric production',
            ],
            [
                'type' => 'number',
                'label' => 'Number of Artisans Involved in Post-Cocoon Activities',
                'required' => true,
                'placeholder' => 'Enter number of artisans',
            ],
            [
                'type' => 'textarea',
                'label' => 'Major Challenges in Post-Cocoon Activities',
                'required' => true,
                'placeholder' => 'Enter major challenges',
            ],
            [
                'type' => 'textarea',
                'label' => 'Government Schemes Available',
                'required' => true,
                'placeholder' => 'Enter government schemes available',
            ],
            [
                'type' => 'textarea',
                'label' => 'Market Linkages',
                'required' => true,
                'placeholder' => 'Enter market linkages',
            ],
        ];
    }

    /**
     * Get the structure for the Sericulture Profile Form (Pre-Cocoon).
     */
    private function getSericulturePreCocoonFormStructure()
    {
        return [
            [
                'type' => 'text',
                'label' => 'Farmer Name',
                'required' => true,
                'placeholder' => 'Enter farmer name',
            ],
            [
                'type' => 'text',
                'label' => 'Village',
                'required' => true,
                'placeholder' => 'Enter village name',
            ],
            [
                'type' => 'text',
                'label' => 'District',
                'required' => true,
                'placeholder' => 'Enter district name',
            ],
            [
                'type' => 'text',
                'label' => 'State',
                'required' => true,
                'placeholder' => 'Enter state name',
            ],
            [
                'type' => 'number',
                'label' => 'Land Holding (in acres)',
                'required' => true,
                'placeholder' => 'Enter land holding',
            ],
            [
                'type' => 'number',
                'label' => 'Area Under Mulberry Cultivation (in acres)',
                'required' => true,
                'placeholder' => 'Enter area under mulberry cultivation',
            ],
            [
                'type' => 'select',
                'label' => 'Mulberry Variety',
                'required' => true,
                'options' => ['V1', 'S36', 'S54', 'G4', 'Other'],
            ],
            [
                'type' => 'select',
                'label' => 'Irrigation Method',
                'required' => true,
                'options' => ['Drip', 'Sprinkler', 'Flood', 'Rainfed', 'Other'],
            ],
            [
                'type' => 'number',
                'label' => 'Number of Crops per Year',
                'required' => true,
                'placeholder' => 'Enter number of crops per year',
            ],
            [
                'type' => 'select',
                'label' => 'Silkworm Race',
                'required' => true,
                'options' => ['Bivoltine', 'Multivoltine', 'Cross Breed', 'Other'],
            ],
            [
                'type' => 'number',
                'label' => 'Average Cocoon Yield (in kg per 100 DFLs)',
                'required' => true,
                'placeholder' => 'Enter average cocoon yield',
            ],
            [
                'type' => 'textarea',
                'label' => 'Major Diseases Encountered',
                'required' => false,
                'placeholder' => 'Enter major diseases encountered',
            ],
            [
                'type' => 'textarea',
                'label' => 'Challenges Faced',
                'required' => true,
                'placeholder' => 'Enter challenges faced',
            ],
        ];
    }

    /**
     * Get the structure for the Sericulture Profile Form (Post-Cocoon).
     */
    private function getSericulturePostCocoonFormStructure()
    {
        return [
            [
                'type' => 'text',
                'label' => 'Reeler/Weaver Name',
                'required' => true,
                'placeholder' => 'Enter reeler/weaver name',
            ],
            [
                'type' => 'text',
                'label' => 'Village',
                'required' => true,
                'placeholder' => 'Enter village name',
            ],
            [
                'type' => 'text',
                'label' => 'District',
                'required' => true,
                'placeholder' => 'Enter district name',
            ],
            [
                'type' => 'text',
                'label' => 'State',
                'required' => true,
                'placeholder' => 'Enter state name',
            ],
            [
                'type' => 'select',
                'label' => 'Type of Activity',
                'required' => true,
                'options' => ['Reeling', 'Weaving', 'Both', 'Other'],
            ],
            [
                'type' => 'select',
                'label' => 'Type of Reeling Unit',
                'required' => false,
                'options' => ['Charka', 'Cottage Basin', 'Multi-end', 'Automatic', 'Not Applicable'],
            ],
            [
                'type' => 'number',
                'label' => 'Number of Basins/Looms',
                'required' => true,
                'placeholder' => 'Enter number of basins/looms',
            ],
            [
                'type' => 'number',
                'label' => 'Annual Raw Silk Production (in kg)',
                'required' => false,
                'placeholder' => 'Enter annual raw silk production',
            ],
            [
                'type' => 'number',
                'label' => 'Annual Fabric Production (in meters)',
                'required' => false,
                'placeholder' => 'Enter annual fabric production',
            ],
            [
                'type' => 'select',
                'label' => 'Source of Cocoons',
                'required' => false,
                'options' => ['Local Market', 'Government Cocoon Market', 'Direct from Farmers', 'Other'],
            ],
            [
                'type' => 'select',
                'label' => 'Market for Products',
                'required' => true,
                'options' => ['Local Market', 'Traders', 'Export', 'Government Agencies', 'Other'],
            ],
            [
                'type' => 'textarea',
                'label' => 'Challenges Faced',
                'required' => true,
                'placeholder' => 'Enter challenges faced',
            ],
            [
                'type' => 'textarea',
                'label' => 'Support Required',
                'required' => true,
                'placeholder' => 'Enter support required',
            ],
        ];
    }

    /**
     * Create a form-specific table for storing responses.
     */
    private function createFormTable($form)
    {
        try {
            // Use the FormSubmission model's method to create the table
            \App\Models\FormSubmission::createFormTable($form);

            Log::info('Form table created successfully', [
                'form_id' => $form->id,
                'form_name' => $form->form_name
            ]);
        } catch (\Exception $e) {
            Log::warning('Could not create form table immediately', [
                'form_id' => $form->id,
                'form_name' => $form->form_name,
                'error' => $e->getMessage()
            ]);
            // Table will be created when first submission is made
        }
    }
}

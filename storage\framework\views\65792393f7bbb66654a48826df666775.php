<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header"><?php echo e(__('Scientist Dashboard')); ?></div>

                <div class="card-body">
                    <h2 class="text-center mb-4">Welcome <?php echo e(Auth::user()->name); ?></h2>

                    <!-- District Information -->
                    <div class="alert alert-info" role="alert">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>District:</strong> <?php echo e($stats['district']); ?>

                                <?php if($stats['district_status']): ?>
                                    <span class="badge <?php echo e($stats['district_status'] === 'Pre-Cocoon' ? 'bg-success' : 'bg-info'); ?> ms-2">
                                        <?php echo e($stats['district_status']); ?>

                                    </span>
                                <?php endif; ?>
                            </div>
                            <div>
                                <strong>SWOT Analysis:</strong>
                                <?php if($stats['swot_completed']): ?>
                                    <span class="badge bg-success">Completed</span>
                                <?php else: ?>
                                    <span class="badge bg-warning">Pending</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Stats Cards -->
                    <div class="row mt-4">
                        <div class="col-md-4 mb-3">
                            <div class="card text-center h-100">
                                <div class="card-body py-3">
                                    <h5 class="card-title">Action Plans</h5>
                                    <h3><?php echo e($stats['action_plans_count']); ?></h3>
                                    <p class="card-text small">Total action plans created</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card text-center h-100">
                                <div class="card-body py-3">
                                    <h5 class="card-title">Status</h5>
                                    <div class="d-flex flex-column align-items-center w-100">
                                        <div class="d-flex justify-content-between w-100 mb-1 bg-warning text-dark rounded px-3 py-1">
                                            <span>Planned:</span>
                                            <span class="fw-bold"><?php echo e($stats['planned_action_plans']); ?></span>
                                        </div>
                                        <div class="d-flex justify-content-between w-100 mb-1 bg-success text-white rounded px-3 py-1">
                                            <span>Completed:</span>
                                            <span class="fw-bold"><?php echo e($stats['completed_action_plans']); ?></span>
                                        </div>
                                        <div class="d-flex justify-content-between w-100 bg-danger text-white rounded px-3 py-1">
                                            <span>Cancelled:</span>
                                            <span class="fw-bold"><?php echo e($stats['cancelled_action_plans']); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card text-center h-100">
                                <div class="card-body py-3">
                                    <h5 class="card-title">Submissions</h5>
                                    <h3><?php echo e($stats['form_submissions']); ?></h3>
                                    <p class="card-text small">Form submissions</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Menu Cards -->
                    <div class="row mt-4">
                        <?php if($stats['swot_completed']): ?>
                        <!-- When SWOT is completed, use 2 columns with more width -->
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-header">Profile & Forms</div>
                                <div class="card-body">
                                    <p>View and fill out available forms including your profile.</p>
                                    <a href="<?php echo e(route('scientist.forms')); ?>" class="btn btn-primary">View Forms</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-header">Action Plans</div>
                                <div class="card-body">
                                    <p>Create and manage action plans and post-action reports.</p>
                                    <a href="<?php echo e(route('scientist.action-plans')); ?>" class="btn btn-primary">Manage Plans</a>
                                </div>
                            </div>
                        </div>
                        <?php else: ?>
                        <!-- When SWOT is not completed, use 3 columns -->
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-header">Profile & Forms</div>
                                <div class="card-body">
                                    <p>View and fill out available forms including your profile.</p>
                                    <a href="<?php echo e(route('scientist.forms')); ?>" class="btn btn-primary">View Forms</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-header">SWOT Analysis</div>
                                <div class="card-body">
                                    <p>Create or update SWOT analysis for your district.</p>
                                    <a href="<?php echo e(route('scientist.swot')); ?>" class="btn btn-primary">Manage SWOT</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-header">Action Plans</div>
                                <div class="card-body">
                                    <p>Create and manage action plans and post-action reports.</p>
                                    <a href="<?php echo e(route('scientist.action-plans')); ?>" class="btn btn-primary">Manage Plans</a>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-<?php echo e($stats['swot_completed'] ? '6' : '4'); ?> mb-3 mx-auto">
                            <div class="card h-100">
                                <div class="card-header">Event Feedback</div>
                                <div class="card-body">
                                    <p>Provide feedback for completed events and activities.</p>
                                    <a href="<?php echo e(route('scientist.feedback')); ?>" class="btn btn-primary">Manage Feedback</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4 text-center">
                        <form action="<?php echo e(route('logout')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="btn btn-danger">Logout</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>



<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\csb\resources\views/scientist/dashboard.blade.php ENDPATH**/ ?>